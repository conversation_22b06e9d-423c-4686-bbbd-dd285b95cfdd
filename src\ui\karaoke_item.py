#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import random
from PySide6.QtQuick import QQuickPaintedItem
from PySide6.QtCore import Property, Signal, QTimer, QRect, QPointF, Qt
from PySide6.QtGui import QPainter, QColor, QFont, QFontMetrics, QLinearGradient, QRadialGradient, QPainterPath, QPen
from PySide6.QtQml import QmlElement

from backend.karaoke_backend import KaraokeBackend

QML_IMPORT_NAME = "UltraStarBasics"
QML_IMPORT_MAJOR_VERSION = 1

@QmlElement
class KaraokeItem(QQuickPaintedItem):
    """Custom QML item for karaoke display"""

    # Signals
    backendChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)

        # Backend reference
        self._backend = None

        # Effect constants (matching backend)
        self.EFFECT_SIMPLE = 0
        self.EFFECT_ZOOM = 1
        self.EFFECT_PARTICLE = 2
        self.EFFECT_BALL = 3
        self.EFFECT_SHIFT = 4
        self.EFFECT_WAVE = 5
        self.EFFECT_PULSE = 6
        self.EFFECT_TYPEWRITER = 7
        self.EFFECT_AEGISUB = 8
        self.EFFECT_YTMUSIC = 9

        # Effect parameters
        self.zoom_factor = 1.5
        self.shift_amount = 8
        self.ball_size = 18
        self.ball_offset = 60
        self.ball_smoothness = 0.8
        self.ball_squash = 0.25
        self.ball_shadow_offset = 5
        self.ball_shadow_alpha = 120
        self.ball_highlight_size = 0.4

        # Wave effect parameters
        self.wave_amplitude = 5.0
        self.wave_length = 24.0
        self.wave_speed = 0.05
        self.wave_max_scale = 1.35
        self.wave_scale_width = 18.0

        # Pulse effect parameters
        self.pulse_min_scale = 0.85
        self.pulse_max_scale = 1.25
        self.pulse_speed = 3.0

        # Particle effect parameters
        self.particle_count = 15
        self.particle_size_min = 2
        self.particle_size_max = 5
        self.particle_speed = 2.0
        self.particle_life = 0.8

        # YTMusic effect parameters
        self.ytmusic_word_delay = 0.05  # Delay between words in seconds
        self.ytmusic_glow_radius = 8.0  # Glow effect radius
        self.ytmusic_glow_intensity = 0.6  # Glow intensity
        self.ytmusic_glow_anticipation = 60.0  # How far ahead the glow extends (pixels) - increased separation
        self.ytmusic_glow_separation = 0.4  # Additional separation between main line and anticipation
        self.ytmusic_wobble_amplitude = 1.2  # Wobble movement amplitude (restored for wave effect)
        self.ytmusic_wobble_speed = 2.5  # Wobble animation speed (restored for wave effect)
        self.ytmusic_active_scale = 1.0  # Scale for active line
        self.ytmusic_inactive_opacity = 0.4  # Opacity for pending text
        self.ytmusic_active_opacity = 1.0  # Opacity for filled text
        self.ytmusic_anticipation_opacity = 0.7  # Opacity for anticipation line
        self.ytmusic_transition_duration = 0.33  # Transition duration in seconds

        # Edge illumination effect parameters
        self.ytmusic_edge_glow_intensity = 0.5  # Intensity of edge illumination (more subtle)
        self.ytmusic_edge_glow_speed = 2.0  # Speed of edge illumination wave (slower)
        self.ytmusic_edge_glow_width = 1.5  # Width of edge illumination effect (thinner)

        # Fonts
        self.font_current = QFont("Arial", 24, QFont.Bold)
        self.font_next = QFont("Arial", 18)

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.setInterval(16)  # ~60 FPS
        self.update_timer.timeout.connect(self.update)
        self.update_timer.start()

        # Set background
        self.setFillColor(QColor(0, 0, 0))

    @Property(KaraokeBackend, notify=backendChanged)
    def backend(self):
        """Return backend"""
        return self._backend

    @backend.setter
    def backend(self, backend):
        """Set backend"""
        if self._backend != backend:
            if self._backend:
                # Disconnect old backend signals
                self._backend.textChanged.disconnect(self.update)
                self._backend.effectChanged.disconnect(self.update)
                self._backend.colorsChanged.disconnect(self.update)
                self._backend.settingsChanged.disconnect(self.update)

            self._backend = backend

            if self._backend:
                # Connect new backend signals
                self._backend.textChanged.connect(self.update)
                self._backend.effectChanged.connect(self.update)
                self._backend.colorsChanged.connect(self.update)
                self._backend.settingsChanged.connect(self.update)

            self.backendChanged.emit()
            self.update()

    def paint(self, painter):
        """Paint the karaoke display"""
        if not self._backend:
            return

        # Set up painter
        painter.setRenderHint(QPainter.TextAntialiasing, True)
        painter.fillRect(self.boundingRect(), QColor(0, 0, 0))

        # Get text data from backend
        sung_text = self._backend.sungText
        current_syllable = self._backend.currentSyllable
        pending_text = self._backend.pendingText
        next_line_text = self._backend.nextLineText
        syllable_progress = self._backend.syllableProgress

        # Get colors from backend
        sung_color = self._backend.sungColor
        pending_color = self._backend.pendingColor
        next_line_color = self._backend.nextLineColor
        ball_color = self._backend.ballColor

        # Configure font for current line
        painter.setFont(self.font_current)
        font_metrics = QFontMetrics(self.font_current)

        # Complete text of current line
        full_text = sung_text + current_syllable + pending_text

        # Calculate positions
        line_width = font_metrics.horizontalAdvance(full_text)
        x = (self.width() - line_width) / 2
        y = self.height() / 2

        # Calculate width up to sung portion
        sung_width = font_metrics.horizontalAdvance(sung_text)
        current_width = font_metrics.horizontalAdvance(current_syllable)

        # Calculate progress width
        progress_width = int(sung_width + (current_width * syllable_progress))

        # Create path for complete text
        text_path = QPainterPath()
        text_path.addText(int(x), int(y), self.font_current, full_text)

        # Draw pending text (background)
        painter.setPen(QPen(Qt.NoPen))
        painter.setBrush(pending_color)
        painter.drawPath(text_path)

        # Apply current effect
        current_effect = self._backend.currentEffect
        
        if current_effect == self.EFFECT_SIMPLE:
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color)
        elif current_effect == self.EFFECT_ZOOM:
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color)
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_zoom_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color)
        elif current_effect == self.EFFECT_PARTICLE:
            self._draw_particle_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color, pending_color)
        elif current_effect == self.EFFECT_BALL:
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color)
            self._draw_ball_effect(painter, x, y, sung_width, current_width, syllable_progress, ball_color)
        elif current_effect == self.EFFECT_SHIFT:
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color)
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_shift_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color)
        elif current_effect == self.EFFECT_WAVE:
            painter.fillRect(self.boundingRect(), QColor(0, 0, 0))
            self._draw_wave_effect(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color)
        elif current_effect == self.EFFECT_PULSE:
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color)
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_pulse_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color)
        elif current_effect == self.EFFECT_TYPEWRITER:
            painter.fillRect(self.boundingRect(), QColor(0, 0, 0))
            self._draw_typewriter_effect(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color)
        elif current_effect == self.EFFECT_AEGISUB:
            painter.fillRect(self.boundingRect(), QColor(0, 0, 0))
            self._draw_aegisub_effect(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color)
        elif current_effect == self.EFFECT_YTMUSIC:
            painter.fillRect(self.boundingRect(), QColor(0, 0, 0))
            self._draw_ytmusic_effect(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color)

        # Draw next line
        if next_line_text:
            painter.setFont(self.font_next)
            next_line_width = QFontMetrics(self.font_next).horizontalAdvance(next_line_text)
            x_next = (self.width() - next_line_width) / 2
            y_next = y + font_metrics.height() + 20

            next_line_path = QPainterPath()
            next_line_path.addText(int(x_next), int(y_next), self.font_next, next_line_text)

            painter.setPen(QPen(Qt.NoPen))
            painter.setBrush(next_line_color)
            painter.drawPath(next_line_path)

    def _draw_simple_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color):
        """Draw simple left-to-right highlighting effect"""
        progress_width = int(sung_width + (current_width * syllable_progress))

        if progress_width > 0:
            painter.save()
            clip_rect = QRect(int(x), 0, progress_width, int(self.height()))
            painter.setClipRect(clip_rect)

            painter.setBrush(sung_color)
            painter.drawPath(text_path)

            painter.restore()

    def _draw_zoom_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color):
        """Draw zoom effect overlay"""
        zoom = 1.0 + (self.zoom_factor - 1.0) * (1.0 - syllable_progress)

        painter.save()

        syllable_x = x + sung_width + current_width / 2
        syllable_y = y

        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), int(self.height()))
        painter.fillRect(syllable_rect, QColor(0, 0, 0))

        painter.translate(syllable_x, syllable_y)
        painter.scale(zoom, zoom)
        painter.translate(-syllable_x, -syllable_y)

        syllable_path = QPainterPath()
        syllable_path.addText(int(x + sung_width), int(y), self.font_current, current_syllable)
        painter.setBrush(sung_color)
        painter.drawPath(syllable_path)

        painter.restore()

    def _draw_particle_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_color, pending_color):
        """Draw particle effect"""
        progress_width = int(sung_width + (current_width * syllable_progress))

        if progress_width > 0:
            # Sung part
            painter.save()
            clip_rect = QRect(int(x), 0, progress_width, int(self.height()))
            painter.setClipRect(clip_rect)
            painter.setBrush(sung_color)
            painter.drawPath(text_path)
            painter.restore()

            # Pending part
            painter.save()
            clip_rect = QRect(int(x + progress_width), 0, int(self.width() - x - progress_width), int(self.height()))
            painter.setClipRect(clip_rect)
            painter.setBrush(pending_color)
            painter.drawPath(text_path)
            painter.restore()

            # Generate particles
            particle_x = x + progress_width
            particle_y = y

            particle_count = int(self.particle_count * (1.0 - syllable_progress) + 2)

            for i in range(particle_count):
                angle = math.radians(random.randint(0, 360))
                distance = random.uniform(0, self.particle_speed * 20 * (1.0 - syllable_progress))

                px = particle_x + math.cos(angle) * distance
                py = particle_y + math.sin(angle) * distance

                size = self.particle_size_max - (distance / (self.particle_speed * 20)) * (self.particle_size_max - self.particle_size_min)
                size = max(self.particle_size_min, size)

                opacity = 1.0 - (distance / (self.particle_speed * 20))
                opacity = max(0.1, opacity)

                lightness = 0.3 + (distance / (self.particle_speed * 20)) * 0.5
                lightness = min(0.8, lightness)

                particle_color = self._get_lighter_color(sung_color, lightness)

                painter.save()
                painter.setOpacity(opacity)
                painter.setBrush(particle_color)
                painter.setPen(QPen(Qt.NoPen))
                painter.drawEllipse(int(px - size/2), int(py - size/2), int(size), int(size))
                painter.restore()

    def _draw_ball_effect(self, painter, x, y, sung_width, current_width, syllable_progress, ball_color):
        """Draw bouncing ball effect"""
        ball_x = x + sung_width + current_width * syllable_progress

        smooth_progress = math.pow(syllable_progress, self.ball_smoothness)
        bounce_phase = smooth_progress * math.pi * 2

        bounce_height = 50
        bounce_factor = 0.5 * (1 + math.sin(bounce_phase - math.pi/2))

        ball_y = y - self.ball_offset - bounce_height * bounce_factor

        vertical_velocity = math.cos(bounce_phase - math.pi/2)
        squash_factor = 1.0 - self.ball_squash * vertical_velocity
        stretch_factor = 1.0 / squash_factor

        painter.save()

        # Draw shadow
        shadow_size = self.ball_size * (1.0 + 0.5 * bounce_factor)
        shadow_alpha = int(self.ball_shadow_alpha * (1.0 - 0.5 * bounce_factor))
        shadow_color = QColor(0, 0, 0, shadow_alpha)

        shadow_y = y - self.ball_offset/2

        painter.setBrush(shadow_color)
        painter.setPen(QPen(Qt.NoPen))
        painter.drawEllipse(
            int(ball_x - shadow_size/2),
            int(shadow_y - shadow_size/8),
            int(shadow_size),
            int(shadow_size/4)
        )

        # Draw ball
        painter.translate(ball_x, ball_y)
        painter.scale(stretch_factor, squash_factor)

        gradient = QRadialGradient(0, 0, self.ball_size)

        darker_color = QColor(
            max(0, ball_color.red() - 50),
            max(0, ball_color.green() - 50),
            max(0, ball_color.blue() - 50)
        )

        lighter_color = QColor(
            min(255, ball_color.red() + 70),
            min(255, ball_color.green() + 70),
            min(255, ball_color.blue() + 70)
        )

        gradient.setColorAt(0.0, lighter_color)
        gradient.setColorAt(0.7, ball_color)
        gradient.setColorAt(1.0, darker_color)

        painter.setBrush(gradient)
        painter.setPen(QPen(Qt.NoPen))
        painter.drawEllipse(
            int(-self.ball_size/2),
            int(-self.ball_size/2),
            self.ball_size,
            self.ball_size
        )

        # Add highlight
        highlight_size = self.ball_size * self.ball_highlight_size
        highlight_offset = -self.ball_size * 0.2

        highlight_gradient = QRadialGradient(
            highlight_offset, highlight_offset, highlight_size
        )
        highlight_gradient.setColorAt(0.0, QColor(255, 255, 255, 180))
        highlight_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))

        painter.setBrush(highlight_gradient)
        painter.drawEllipse(
            int(highlight_offset - highlight_size/2),
            int(highlight_offset - highlight_size/2),
            int(highlight_size),
            int(highlight_size)
        )

        painter.restore()

    def _draw_shift_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color):
        """Draw shift effect overlay"""
        shift = self.shift_amount * (1.0 - syllable_progress)

        painter.save()

        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), int(self.height()))
        painter.fillRect(syllable_rect, QColor(0, 0, 0))

        syllable_path = QPainterPath()
        syllable_path.addText(int(x + sung_width), int(y - shift), self.font_current, current_syllable)

        painter.setBrush(sung_color)
        painter.drawPath(syllable_path)

        painter.restore()

    def _draw_wave_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color):
        """Draw wave effect with letter deformation"""
        font_metrics = QFontMetrics(self.font_current)

        sung_text_width = font_metrics.horizontalAdvance(sung_text)
        current_syllable_width = font_metrics.horizontalAdvance(current_syllable)
        progress_in_current_syllable = current_syllable_width * syllable_progress

        progress_px = sung_text_width + progress_in_current_syllable

        current_x = x

        # Draw sung text with wave effect
        for idx, char in enumerate(sung_text):
            char_width = font_metrics.horizontalAdvance(char)
            char_center = current_x + char_width / 2

            dist_from_front = abs(char_center - (x + progress_px))

            scale_factor = 1.0
            if dist_from_front < self.wave_scale_width * 3:
                scale_factor = 1.0 + (self.wave_max_scale - 1.0) * math.exp(-0.3 * (dist_from_front / self.wave_scale_width) ** 2)

            painter.save()
            painter.translate(char_center, y)
            painter.scale(scale_factor, scale_factor)

            char_path = QPainterPath()
            char_path.addText(QPointF(-char_width/2, 0), self.font_current, char)

            painter.setBrush(sung_color)
            painter.setPen(QPen(Qt.NoPen))
            painter.drawPath(char_path)

            painter.restore()

            current_x += char_width

        # Draw current syllable
        if current_syllable:
            for idx, char in enumerate(current_syllable):
                char_width = font_metrics.horizontalAdvance(char)
                char_center = current_x + char_width / 2

                char_progress = (current_x - x - sung_text_width) / current_syllable_width if current_syllable_width > 0 else 0
                is_karaoke = char_progress <= syllable_progress

                if is_karaoke:
                    dist_from_front = abs(char_center - (x + progress_px))

                    scale_factor = 1.0
                    if dist_from_front < self.wave_scale_width * 3:
                        scale_factor = 1.0 + (self.wave_max_scale - 1.0) * math.exp(-0.3 * (dist_from_front / self.wave_scale_width) ** 2)

                    painter.save()
                    painter.translate(char_center, y)
                    painter.scale(scale_factor, scale_factor)

                    char_path = QPainterPath()
                    char_path.addText(QPointF(-char_width/2, 0), self.font_current, char)

                    # Use lighter color for active syllable
                    active_color = self._get_lighter_color(sung_color, 0.4)
                    painter.setBrush(active_color)
                    painter.setPen(QPen(Qt.NoPen))
                    painter.drawPath(char_path)

                    painter.restore()
                else:
                    painter.setBrush(pending_color)
                    painter.setPen(QPen(Qt.NoPen))
                    char_path = QPainterPath()
                    char_path.addText(QPointF(current_x, y), self.font_current, char)
                    painter.drawPath(char_path)

                current_x += char_width

        # Draw pending text
        if pending_text:
            painter.setBrush(pending_color)
            painter.setPen(QPen(Qt.NoPen))
            pending_path = QPainterPath()
            pending_path.addText(QPointF(current_x, y), self.font_current, pending_text)
            painter.drawPath(pending_path)

    def _draw_pulse_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable, sung_color):
        """Draw pulse effect overlay"""
        pulse_phase = syllable_progress * self.pulse_speed * math.pi
        scale_factor = self.pulse_min_scale + (self.pulse_max_scale - self.pulse_min_scale) * 0.5 * (1.0 + math.sin(pulse_phase))

        painter.save()

        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), int(self.height()))
        painter.fillRect(syllable_rect, QColor(0, 0, 0))

        char_center_x = x + sung_width + current_width / 2
        char_center_y = y

        painter.translate(char_center_x, char_center_y)
        painter.scale(scale_factor, scale_factor)
        painter.translate(-char_center_x, -char_center_y)

        syllable_path = QPainterPath()
        syllable_path.addText(int(x + sung_width), int(y), self.font_current, current_syllable)

        brightness = int(128 + 127 * ((scale_factor - self.pulse_min_scale) / (self.pulse_max_scale - self.pulse_min_scale)))
        pulse_color = QColor(0, brightness, 0)

        painter.setBrush(pulse_color)
        painter.drawPath(syllable_path)

        painter.restore()

    def _draw_typewriter_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color):
        """Draw typewriter effect"""
        font_metrics = QFontMetrics(self.font_current)

        # Draw sung text
        if sung_text:
            painter.setBrush(sung_color)
            painter.setPen(QPen(Qt.NoPen))
            sung_path = QPainterPath()
            sung_path.addText(x, y, self.font_current, sung_text)
            painter.drawPath(sung_path)

        # Draw current syllable with typewriter effect
        if current_syllable:
            char_count = len(current_syllable)
            visible_chars = min(char_count, int(char_count * syllable_progress) + 1)

            current_x = x + sung_width

            for i in range(visible_chars):
                char = current_syllable[i]
                char_width = font_metrics.horizontalAdvance(char)

                char_progress = (i + 1) / char_count
                char_opacity = min(1.0, (syllable_progress - (char_progress - 0.1)) / 0.2)
                char_opacity = max(0.0, char_opacity)

                painter.save()
                painter.setOpacity(char_opacity)

                char_path = QPainterPath()
                char_path.addText(int(current_x), int(y), self.font_current, char)

                is_active_char = (i == visible_chars - 1)

                if is_active_char:
                    active_color = self._get_lighter_color(sung_color, 0.4)
                    painter.setBrush(active_color)
                else:
                    painter.setBrush(sung_color)

                painter.setPen(QPen(Qt.NoPen))
                painter.drawPath(char_path)

                painter.restore()

                current_x += char_width

            # Draw pending characters in syllable
            if visible_chars < char_count:
                pending_x = current_x

                for i in range(visible_chars, char_count):
                    char = current_syllable[i]
                    char_width = font_metrics.horizontalAdvance(char)

                    char_path = QPainterPath()
                    char_path.addText(int(pending_x), int(y), self.font_current, char)

                    painter.save()
                    painter.setOpacity(0.5)
                    painter.setPen(QPen(Qt.NoPen))
                    painter.setBrush(pending_color)
                    painter.drawPath(char_path)
                    painter.restore()

                    pending_x += char_width

                # Draw cursor
                cursor_blink = (math.sin(syllable_progress * 10) > 0)
                if cursor_blink:
                    cursor_rect = QRect(int(current_x), int(y - font_metrics.ascent()), 2, font_metrics.height())
                    painter.fillRect(cursor_rect, sung_color)

        # Draw pending text
        if pending_text:
            painter.setBrush(pending_color)
            painter.setPen(QPen(Qt.NoPen))
            pending_path = QPainterPath()
            pending_path.addText(x + sung_width + current_width, y, self.font_current, pending_text)
            painter.drawPath(pending_path)

    def _draw_aegisub_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color):
        """Draw Aegisub-style 3D effect"""
        font_metrics = QFontMetrics(self.font_current)

        current_x = x
        smooth_progress = math.pow(syllable_progress, 0.8)

        # Draw sung text with 3D effects
        if sung_text:
            for idx, char in enumerate(sung_text):
                char_width = font_metrics.horizontalAdvance(char)

                random.seed(idx + 1)

                rotation_x = random.uniform(-5, 5)
                rotation_y = random.uniform(-5, 5)
                rotation_z = random.uniform(-5, 5)

                scale_x = random.uniform(0.97, 1.03)
                scale_y = random.uniform(0.97, 1.03)

                self._draw_aegisub_char(painter, current_x, y, char, sung_color,
                                       rotation_x, rotation_y, rotation_z,
                                       scale_x, scale_y, 1.0)

                current_x += char_width

        # Draw current syllable with enhanced effects
        if current_syllable:
            active_color = self._get_lighter_color(sung_color, 0.4)
            bold_font = QFont(self.font_current)
            bold_font.setBold(True)

            for idx, char in enumerate(current_syllable):
                char_width = font_metrics.horizontalAdvance(char)

                char_progress = (idx / len(current_syllable)) if len(current_syllable) > 0 else 0
                is_karaoke = char_progress <= syllable_progress

                if is_karaoke:
                    random.seed(idx + 100)

                    phase = smooth_progress * math.pi * 1.5
                    rotation_factor = 0.5 * (1 + math.sin(phase))

                    rotation_x = 15 * rotation_factor * random.uniform(0.7, 1.0)
                    rotation_y = 15 * rotation_factor * random.uniform(0.7, 1.0)
                    rotation_z = 15 * rotation_factor * random.uniform(0.7, 1.0)

                    scale_phase = smooth_progress * math.pi * 2 * 1.5
                    scale_factor = 1.0 + 0.15 * (1 + math.sin(scale_phase)) / 2

                    scale_x = scale_factor * random.uniform(0.9, 1.2)
                    scale_y = scale_factor * random.uniform(0.9, 1.2)

                    bold_scale = 1.0 + 0.15 * (1.0 - smooth_progress)

                    self._draw_aegisub_char(painter, current_x, y, char, active_color,
                                           rotation_x, rotation_y, rotation_z,
                                           scale_x * bold_scale, scale_y * bold_scale, 1.0,
                                           bold_font)
                else:
                    self._draw_aegisub_char(painter, current_x, y, char, pending_color,
                                           0, 0, 0, 1.0, 1.0, 1.0, self.font_current)

                current_x += char_width

        # Draw pending text
        if pending_text:
            painter.setPen(QPen(Qt.NoPen))
            painter.setBrush(pending_color)
            pending_path = QPainterPath()
            pending_path.addText(current_x, y, self.font_current, pending_text)
            painter.drawPath(pending_path)

    def _draw_aegisub_char(self, painter, x, y, char, color, rot_x, rot_y, rot_z, scale_x, scale_y, opacity, font=None):
        """Draw character with 3D Aegisub-style transformations"""
        if abs(rot_x) < 0.01 and abs(rot_y) < 0.01 and abs(rot_z) < 0.01 and abs(scale_x - 1.0) < 0.01 and abs(scale_y - 1.0) < 0.01:
            painter.save()

            char_font = font if font else self.font_current

            painter.setOpacity(opacity)

            painter.setPen(QPen(Qt.NoPen))
            painter.setBrush(color)

            char_path = QPainterPath()
            char_path.addText(x, y, char_font, char)
            painter.drawPath(char_path)

            painter.restore()
            return

        painter.save()

        char_font = font if font else self.font_current

        char_path = QPainterPath()
        char_width = QFontMetrics(char_font).horizontalAdvance(char)

        char_center_x = x + char_width / 2
        char_center_y = y

        painter.translate(char_center_x, char_center_y)

        if abs(rot_z) > 0.01:
            painter.rotate(rot_z)

        final_scale_x = scale_x
        final_scale_y = scale_y

        if abs(rot_x) > 0.01:
            scale_y_factor = math.cos(math.radians(rot_x))
            final_scale_y *= scale_y_factor

        if abs(rot_y) > 0.01:
            scale_x_factor = math.cos(math.radians(rot_y))
            final_scale_x *= scale_x_factor

        painter.scale(final_scale_x, final_scale_y)

        painter.translate(-char_center_x, -char_center_y)

        painter.setOpacity(opacity)

        char_path.addText(x, y, char_font, char)

        # Simple blur effect
        blur_steps = 2
        blur_opacity = 0.1

        for i in range(blur_steps):
            angle = (i / blur_steps) * math.pi * 2
            offset_x = math.cos(angle) * 1.0
            offset_y = math.sin(angle) * 1.0

            painter.save()
            painter.translate(offset_x, offset_y)
            painter.setOpacity(blur_opacity)
            painter.setPen(QPen(Qt.NoPen))
            painter.setBrush(color)
            painter.drawPath(char_path)
            painter.restore()

        painter.setPen(QPen(Qt.NoPen))
        painter.setBrush(color)
        painter.drawPath(char_path)

        painter.restore()

    def _draw_ytmusic_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text, sung_color, pending_color):
        """Draw YTMusic-style effect with 3 distinct lines: pending, filled, and anticipation"""
        import time

        current_time = time.time()
        full_text = sung_text + current_syllable + pending_text

        # Calculate progress width (same as simple effect)
        progress_width = int(sung_width + (current_width * syllable_progress))

        # Check if line is completely filled (for wobble effect activation)
        total_text_width = QFontMetrics(self.font_current).horizontalAdvance(full_text)
        is_line_complete = progress_width >= total_text_width

        # Add wobble effect only when line is completely filled
        wobble_offset = 0
        if is_line_complete:
            wobble_phase = current_time * self.ytmusic_wobble_speed
            wobble_offset = math.sin(wobble_phase) * self.ytmusic_wobble_amplitude

        # 1. LÍNEA PENDIENTE: Draw pending text (background) - normal position
        self._draw_ytmusic_pending_line(painter, x, y + wobble_offset, full_text, pending_color)

        # 2. LÍNEA DE ANTICIPACIÓN: Draw anticipation line with separation and gradient effect
        if self.ytmusic_glow_intensity > 0 and progress_width > 0:
            anticipation_start = progress_width + self.ytmusic_glow_separation
            anticipation_width = anticipation_start + self.ytmusic_glow_anticipation
            self._draw_ytmusic_anticipation_line(painter, x, y + wobble_offset, anticipation_start, anticipation_width, full_text, sung_color)

        # 3. LÍNEA COLOREADA: Draw filled text - normal position, clipped to progress
        if progress_width > 0:
            self._draw_ytmusic_filled_line(painter, x, y + wobble_offset, progress_width, full_text, sung_color, syllable_progress)

        # 4. EFECTO DE ILUMINACIÓN DE BORDES: Draw edge illumination wave effect
        if progress_width > 0:
            self._draw_ytmusic_edge_illumination(painter, x, y + wobble_offset, progress_width, full_text, sung_color, current_time)

    def _draw_ytmusic_pending_line(self, painter, x, y, full_text, pending_color):
        """Draw the pending text line (background)"""
        painter.save()
        painter.setOpacity(self.ytmusic_inactive_opacity)
        painter.setBrush(pending_color)
        painter.setPen(QPen(Qt.NoPen))

        text_path = QPainterPath()
        text_path.addText(QPointF(x, y), self.font_current, full_text)
        painter.drawPath(text_path)

        painter.restore()

    def _draw_ytmusic_anticipation_line(self, painter, x, y, anticipation_start, anticipation_width, full_text, sung_color):
        """Draw the anticipation glow with gradient effect that appears to emerge from main line"""
        painter.save()

        # Create clipping rect for the anticipation area (separated from main progress)
        clip_rect = QRect(int(x + anticipation_start), 0, int(anticipation_width - anticipation_start), int(self.height()))
        painter.setClipRect(clip_rect)

        # Create gradient effect that makes anticipation line appear to "emerge" from main line
        gradient = QLinearGradient(x + anticipation_start, y, x + anticipation_width, y)

        # Create colors for gradient effect
        main_color = self._get_lighter_color(sung_color, 0.2)  # Similar to main line
        anticipation_color = self._get_lighter_color(sung_color, 0.7)  # Much lighter
        fade_color = self._get_lighter_color(sung_color, 0.9)  # Very light

        # Set gradient stops to create smooth "emerging" effect
        main_color.setAlphaF(0.9)  # Start with similar opacity to main line
        anticipation_color.setAlphaF(self.ytmusic_anticipation_opacity)
        fade_color.setAlphaF(0.1)  # Fade out at the end

        gradient.setColorAt(0.0, main_color)  # Start similar to main line
        gradient.setColorAt(0.2, anticipation_color)  # Quick transition to anticipation color
        gradient.setColorAt(0.8, anticipation_color)  # Hold anticipation color
        gradient.setColorAt(1.0, fade_color)  # Smooth fade out at the end

        # Draw glow effect first
        if self.ytmusic_glow_intensity > 0:
            glow_steps = 6

            for i in range(glow_steps):
                glow_radius = (i + 1) * (self.ytmusic_glow_radius / glow_steps)
                glow_opacity = self.ytmusic_glow_intensity * (1.0 - i / glow_steps) * 0.3

                painter.setOpacity(glow_opacity)

                # Use gradient for glow as well
                glow_gradient = QLinearGradient(x + anticipation_start, y, x + anticipation_width, y)
                glow_main = self._get_lighter_color(sung_color, 0.5)
                glow_bright = self._get_lighter_color(sung_color, 0.9)

                glow_main.setAlphaF(glow_opacity * 0.8)
                glow_bright.setAlphaF(glow_opacity * 0.4)

                glow_gradient.setColorAt(0.0, glow_main)
                glow_gradient.setColorAt(1.0, glow_bright)

                painter.setBrush(glow_gradient)
                painter.setPen(QPen(Qt.NoPen))

                # Draw glow offsets
                for angle in range(0, 360, 60):
                    offset_x = math.cos(math.radians(angle)) * glow_radius
                    offset_y = math.sin(math.radians(angle)) * glow_radius

                    glow_path = QPainterPath()
                    glow_path.addText(QPointF(x + offset_x, y + offset_y), self.font_current, full_text)
                    painter.drawPath(glow_path)

        # Draw the anticipation text with gradient
        painter.setOpacity(1.0)
        painter.setBrush(gradient)
        painter.setPen(QPen(Qt.NoPen))

        text_path = QPainterPath()
        text_path.addText(QPointF(x, y), self.font_current, full_text)
        painter.drawPath(text_path)

        painter.restore()

    def _draw_ytmusic_filled_line(self, painter, x, y, progress_width, full_text, sung_color, syllable_progress):
        """Draw the filled text line (main progress)"""
        painter.save()

        # Create clipping rect for the filled portion
        clip_rect = QRect(int(x), 0, progress_width, int(self.height()))
        painter.setClipRect(clip_rect)

        # Draw filled text with full opacity
        painter.setOpacity(self.ytmusic_active_opacity)

        # Slightly enhance color based on progress
        color_enhancement = 0.1 + 0.1 * syllable_progress
        enhanced_color = self._get_lighter_color(sung_color, color_enhancement)
        painter.setBrush(enhanced_color)
        painter.setPen(QPen(Qt.NoPen))

        text_path = QPainterPath()
        text_path.addText(QPointF(x, y), self.font_current, full_text)
        painter.drawPath(text_path)

        painter.restore()

    def _draw_ytmusic_progress_edge(self, painter, edge_x, edge_y, color):
        """Draw bright highlight at the current progress edge"""
        if self.ytmusic_glow_intensity <= 0:
            return

        painter.save()

        # Create a very bright edge color
        edge_color = self._get_lighter_color(color, 0.9)

        # Draw multiple concentric circles for edge highlight
        edge_radii = [3.0, 6.0, 9.0, 12.0]
        edge_opacities = [0.9, 0.7, 0.5, 0.3]

        for radius, opacity in zip(edge_radii, edge_opacities):
            painter.setOpacity(opacity * self.ytmusic_glow_intensity)
            painter.setBrush(edge_color)
            painter.setPen(QPen(Qt.NoPen))

            # Draw circular highlight at the progress edge
            painter.drawEllipse(
                int(edge_x - radius),
                int(edge_y - radius),
                int(radius * 2),
                int(radius * 2)
            )

        # Add a vertical line effect at the edge for more definition
        painter.setOpacity(0.8 * self.ytmusic_glow_intensity)
        line_color = self._get_lighter_color(color, 0.8)
        painter.setBrush(line_color)

        # Draw a thin vertical line at the progress edge
        line_width = 2.0
        line_height = 40.0
        painter.drawRect(
            int(edge_x - line_width/2),
            int(edge_y - line_height/2),
            int(line_width),
            int(line_height)
        )

        painter.restore()

    def _draw_ytmusic_edge_illumination(self, painter, x, y, progress_width, full_text, sung_color, current_time):
        """Draw edge illumination wave effect that travels along letter borders"""
        if self.ytmusic_edge_glow_intensity <= 0:
            return

        painter.save()

        # Create clipping rect for the filled portion only
        clip_rect = QRect(int(x), 0, progress_width, int(self.height()))
        painter.setClipRect(clip_rect)

        # Calculate wave phase for edge illumination
        edge_wave_phase = current_time * self.ytmusic_edge_glow_speed

        # Create bright edge illumination color (same as anticipation line)
        edge_color = self._get_lighter_color(sung_color, 0.7)
        edge_color.setAlphaF(self.ytmusic_edge_glow_intensity)

        # Create text path for edge detection
        text_path = QPainterPath()
        text_path.addText(QPointF(x, y), self.font_current, full_text)

        # Draw multiple edge illumination layers with wave effect
        for i in range(3):
            # Calculate wave offset for this layer
            wave_offset = math.sin(edge_wave_phase + i * 0.5) * self.ytmusic_edge_glow_width

            # Set opacity for this layer
            layer_opacity = self.ytmusic_edge_glow_intensity * (1.0 - i * 0.3)
            painter.setOpacity(layer_opacity)

            # Create stroke pen for edge effect
            edge_pen = QPen(edge_color)
            edge_pen.setWidth(int(self.ytmusic_edge_glow_width + wave_offset))
            edge_pen.setJoinStyle(Qt.RoundJoin)
            edge_pen.setCapStyle(Qt.RoundCap)

            painter.setPen(edge_pen)
            painter.setBrush(Qt.NoBrush)

            # Draw the text outline with wave effect
            painter.drawPath(text_path)

        painter.restore()

    def _get_lighter_color(self, base_color, lightness_factor=0.5):
        """Generate lighter color based on base color"""
        r = base_color.red()
        g = base_color.green()
        b = base_color.blue()

        new_r = int(r + (255 - r) * lightness_factor)
        new_g = int(g + (255 - g) * lightness_factor)
        new_b = int(b + (255 - b) * lightness_factor)

        return QColor(new_r, new_g, new_b, base_color.alpha())
