/* Hides the original lyrics, to only show our own.  */
#tab-renderer[page-type='MUSIC_PAGE_TYPE_TRACK_LYRICS'] > * {
  display: none !important;
}

#tab-renderer[page-type='MUSIC_PAGE_TYPE_TRACK_LYRICS']
  > #synced-lyrics-container {
  display: block !important;
}

/* Hide the scrollbar in the lyrics-tab */
#tab-renderer[page-type='MUSIC_PAGE_TYPE_TRACK_LYRICS'] {
  scrollbar-width: none;
}

@property --lyrics-duration {
  syntax: '<time>';
  inherits: false;
  initial-value: 2s;
}

/* Variables are overridden by selected line effect */
:root {
  /* Layout */
  --global-margin: 0.7rem;
  --lyrics-padding: 0;

  /* Typography */
  --lyrics-font-family: Satoshi, Avenir, -apple-system, BlinkMacSystemFont,
    Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue,
    sans-serif;
  --lyrics-font-size: clamp(1.4rem, 1.1vmax, 3rem);
  --lyrics-line-height: var(--ytmusic-body-line-height);
  --lyrics-width: 100%;

  /* Inactive Lyrics */
  --lyrics-inactive-font-weight: 400;
  --lyrics-inactive-opacity: 0.33;
  --lyrics-inactive-scale: 1;
  --lyrics-inactive-offset: 0;

  /* Active Lyrics */
  --lyrics-active-font-weight: 700;
  --lyrics-active-opacity: 1;
  --lyrics-active-scale: 1;
  --lyrics-active-offset: 0;

  --lyrics-duration: 2s;

  /* Animations */
  --lyrics-animations: lyrics-glow var(--lyrics-glow-duration) forwards,
    lyrics-wobble var(--lyrics-wobble-duration) forwards;
  --lyrics-scale-duration: 0.166s;
  --lyrics-opacity-transition: 0.33s;
  --lyrics-glow-duration: var(--lyrics-duration);
  --lyrics-wobble-duration: calc(var(--lyrics-duration) / 2);

  /* Colors */
  --glow-color: rgba(255, 255, 255, 0.5);
}

.lyric-container {
  padding-top: 16px;
}

.description {
  font-size: clamp(1.4rem, 1.1vmax, 3rem) !important;
  text-align: left !important;
}

.synced-line {
  width: var(--lyrics-width, 100%);

  & .text-lyrics {
    cursor: pointer;
    /*fix cuted lyrics-glow and romanized j at line start */
    padding-left: 1.5rem;
  }

  & .text-lyrics > .romaji {
    color: var(--ytmusic-text-secondary) !important;
    font-size: calc(var(--lyrics-font-size) * 0.7) !important;
    font-style: italic !important;
  }
}

.plain-lyrics .romaji {
  color: var(--ytmusic-text-secondary) !important;
  font-size: calc(var(--lyrics-font-size) * 0.7) !important;
  font-style: italic !important;
}

.plain-lyrics .lrc-header {
  color: var(--ytmusic-color-grey5) !important;
  scale: 0.9;
  height: fit-content;
  padding: 0;
  padding-block: 0.2em;
}

.synced-lyrics {
  display: block;
  justify-content: left;
  text-align: left;
  margin: 0.5rem 20px 0.5rem 0;
  transition: all 0.3s ease-in-out;
}

.warning-lyrics {
  color: var(--ytmusic-text-secondary) !important;
  font-style: italic;
}

.text-lyrics {
  font-family: var(--lyrics-font-family) !important;
  font-size: var(--lyrics-font-size) !important;
  font-weight: var(--lyrics-inactive-font-weight) !important;
  line-height: var(--lyrics-line-height) !important;
  padding-top: var(--lyrics-padding);
  padding-bottom: var(--lyrics-padding);
  scale: var(--lyrics-inactive-scale);
  translate: var(--lyrics-inactive-offset);
  transition: scale var(--lyrics-scale-duration), translate 0.3s ease-in-out;

  display: block;
  text-align: left;
  margin: var(--global-margin) 0;
  transform-origin: 0 50%;
}

.text-lyrics > span > span {
  display: inline-block;
  white-space: pre-wrap;
  opacity: var(--lyrics-inactive-opacity);
  transition: opacity var(--lyrics-opacity-transition);
}

.current .text-lyrics {
  font-weight: var(--lyrics-active-font-weight) !important;
  scale: var(--lyrics-active-scale);
  translate: var(--lyrics-active-offset);
}

.current .text-lyrics > span > span {
  opacity: var(--lyrics-active-opacity);
  animation: var(--lyrics-animations);
}

.lyrics-renderer {
  display: flex;
  flex-direction: column;
}

.lyrics-picker {
  height: 5em;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding-block: 1em;
}

.lyrics-picker-content {
  display: flex;
  width: 50%;

  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.lyrics-picker-content-label {
  display: flex;
  overflow: hidden;
  width: 100%;
  /* padding-block: 5%; */
}

.lyrics-picker-content-dots {
  display: block;
  list-style: none;
}

.lyrics-picker-item {
  display: flex;
  height: 100%;
  min-width: 100%;

  justify-content: center;
  align-items: center;

  transition: transform 0.25s ease-in-out;
}

.lyrics-picker-dot {
  display: inline-block;
  cursor: pointer;
  width: 5px;
  height: 5px;
  margin: 0 4px 0;
  border-radius: 200px;
  border: 1px solid #6e7c7c7f;
}

.lyrics-picker-left,
.lyrics-picker-right {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease;
  border-radius: 25%;

  &:hover {
    background-color: hsla(0, 0%, 100%, 0.1);
  }
}

.lyrics-renderer-sticky {
  position: sticky;
  top: var(--top, 0);
  z-index: 100;
  backdrop-filter: blur(5px);

  transition: top 325ms ease-in-out;
}

/* Animations */
@keyframes lyrics-wobble {
  from {
    transform: translateY(0px);
  }
  33.33% {
    transform: translateY(1.75px);
  }
  66.66% {
    transform: translateY(-1.75px);
  }
  to {
    transform: translateY(0px);
  }
}

@keyframes lyrics-glow {
  0% {
    text-shadow: 0 0 1.5rem var(--glow-color);
  }
  to {
    text-shadow: 0 0 0 var(--glow-color);
  }
}
