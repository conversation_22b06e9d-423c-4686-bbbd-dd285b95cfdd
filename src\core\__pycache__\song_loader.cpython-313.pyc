�

    �ohN_  �                   ��  � S SK r S SKrS SKJrJr  S SKJrJrJrJ	r	  \ " S S5      5       r
\ " S S5      5       r\ " S S	5      5       r " S
 S5      r
\S:X  Ga  S SKr\" \R"                  5      S
:�  a�  \R"                  S
   r\
R'                  \5      r\(       a�  \" S\R,                   S\R.                   35        \" S\R0                   S\R2                   35        \" S\R5                  5        35        \" S\" \R6                  5       35        \R6                  (       aD  \R6                  S    r\" S\R;                  5        35        \" S\" \R<                  5       35        ggg\" S5        gg)�    N)�	dataclass�field)�List�Dict�Optional�Tuplec                   �L   � \ rS rSr% Sr\\S'   \\S'   \\S'   \\S'   \\S'   Srg	)
�Note�
   u.   Representa una nota individual en una canción�type�start�length�pitch�text� N)	�__name__�
__module__�__qualname__�__firstlineno__�__doc__�str�__annotations__�int�__static_attributes__r   �    �]   C:\Users\<USER>\Desktop\Bacup\Widget_def_versión_1\ultrastar_basics\src\core\song_loader.pyr
   r
   
   s   � �8�

�I��J��K��J�

�Ir   r
   c                   �\   � \ rS rSr% Sr\\S'   \" \S9r	\
\   \S'   Sr\\S'   S\
4S	 jrS
rg)�Line�   u.   Representa una línea de letra en una canciónr
   ��default_factory�notesr   �end�returnc                 �F   � SR                  S U R                   5       5      $ )u'   Devuelve el texto completo de la línea� c              3   �8   #   � U  H  oR                   v �  M     g 7f)N)r   )�.0�notes     r   �	<genexpr>� Line.get_text.<locals>.<genexpr>   s   � � �8�Z�T�y�y�Z�s   �)�joinr"   ��selfs    r   �get_text�
Line.get_text   s   � ��w�w�8�T�Z�Z�8�8�8r   r   N)r   r   r   r   r   r   r   r   �listr"   r   r
   r#   r   r/   r   r   r   r   r   r      s2   � �8��J��d�3�E�4��:�3��C��L�9�#� 9r   r   c                   �`  � \ rS rSr% SrSr\\S'   Sr\\S'   Sr	\\S'   Sr
\\S'   Sr\\S	'   Sr
\\S
'   Sr\\S'   Sr\\S'   Sr\\S
'   Sr\\S'   Sr\\S'   Sr\\S'   \" \S9r\\   \S'   Sr\\S'   Sr\\S'   S\4S jrS\4S jrS\4S jrS\S\4S jrS\S\ \!\   \!\"   \4   4S jr#Sr$g) �Song�    u:   Representa una canción completa con sus metadatos y notasr&   �title�artist�mp3_file�        �bpm�gap�cover�
background�video�genre�edition�languager   �yearr    �lines�	file_path�folder_pathr$   c                 �   � [         R                  R                  U R                  U R                  5      n[         R                  R                  U5      (       a  U$  [         R                  " U R                  5       Vs/ s H+  nUR                  5       R                  S5      (       d  M)  UPM-     nnU(       a>  US   U l        [         R                  R                  U R                  U R                  5      $  U$ s  snf ! [         a     U$ f = f)z(Devuelve la ruta completa al archivo MP3�.mp3r   )
�os�pathr,   rD   r7   �exists�listdir�lower�endswith�	Exception)r.   �mp3_path�f�	mp3_filess       r   �get_full_mp3_path�Song.get_full_mp3_path6   s�   � ��7�7�<�<�� 0� 0�$�-�-�@�� �7�7�>�>�(�#�#��O�		�$&�J�J�t�/?�/?�$@� 7�$@�q��7�7�9�-�-�f�5� �$@�I� 7� � )�!���
��w�w�|�|�D�$4�$4�d�m�m�D�D� � ���7�� � 	�� ��		�s+   �"C? �>(C:�*C:�0AC? �:C? �?
D
�D
c                 �  � U R                   (       GdI   / SQnU H�  n[        R                  " U R                  5       Vs/ s Hi  nUR	                  5       R                  U5      (       d  M)  SUR	                  5       ;   d*  SUR	                  5       ;   d  SUR	                  5       ;   d  Mg  UPMk     nnU(       d  M�  US   U l           O   U R                   (       dq  U Hk  n[        R                  " U R                  5       Vs/ s H*  o3R	                  5       R                  U5      (       d  M(  UPM,     nnU(       d  Ma  US   U l           O   U R                   (       d  g[        R                  R                  U R                  U R                   5      n[        R                  R                  U5      (       a  U$ gs  snf s  snf ! [         a     N�f = f)z0Devuelve la ruta completa a la imagen de portada)z.jpgz.jpegz.pngz.gifz.bmpr;   �front�portadar   r&   )
r;   rG   rJ   rD   rK   rL   rM   rH   r,   rI   )r.   �image_extensions�extrO   �potential_covers�images�
cover_paths          r   �get_full_cover_path�Song.get_full_cover_pathM   ss  � ��z�z�z�
�#L� �+�C�35�:�:�d�>N�>N�3O� (p�3O�a�)*����);�);�C�)@� )*�'.�!�'�'�)�';�w�!�'�'�)�?S�W`�de�dk�dk�dm�Wm� )*�3O�$� (p� (�'�%5�a�%8��
��
 ,� �z�z�/��-/�Z�Z��8H�8H�-I�!e�-I��W�W�Y�M_�M_�`c�Md�!�-I��!e�!�6�)/���D�J�!�	  0� �:�:���W�W�\�\�$�"2�"2�D�J�J�?�
� �7�7�>�>�*�%�%��� ��7(p�� "f�� � 
��
�sS   �+G �(F7�+:F7�)F7�/G �:AG � 'F<�+F<�1G �<G �G �7
G �
G�
Gc                 �B   � U R                   S:  a  gU R                   S-  $ )zDevuelve el GAP en segundosr   r8   g     @�@)r:   r-   s    r   �get_gap_seconds�Song.get_gap_secondsq   s#   � � �8�8�a�<�� �x�x�&� � r   �beatc                 �j   � U R                   S::  a  gUS-  U R                   -  U R                  5       -   $ )z&Convierte un beat en tiempo (segundos)r   �<   )r9   r^   )r.   r`   s     r   �
get_beat_time�Song.get_beat_timez   s1   � ��8�8�q�=���r�	�D�H�H�$��(<�(<�(>�>�>r   �current_timec                 �  � U R                  5       nX:  a  gX-
  U R                  -  S-  nSnU R                   H6  nX5R                  :�  a  X5R                  ::  a  Un  OX5R                  :  d  M6    O   U(       d  gSn[        UR                  5       H�  u  pxUR                  UR                  -   n	X8R                  :�  d  M/  X9:  d  M6  UnX8R                  -
  UR                  -  n
SnU
S:  a  U
S-  nU
SUSU-
  -  -
  -  n
OU
S:�  a  U
n
OU
n
[        S[        SU
5      5      nXFU4s  $    UR                  (       Ga  UR                  S	   nUR                  S
   nX?R                  :  a  UR                  U-
  nUS:  a  XOS4$ USS4$ UUR                  UR                  -   :�  a  UUS4$ [        [        UR                  5      S-
  5       Hv  nUR                  U   nUR                  US-      nUR                  UR                  -   n	X9:�  d  MD  UUR                  :  d  MV  X9-
  UR                  U-
  :  a  XHS4s  $ UUS4s  $    USS4$ )uf   
Obtiene la línea y nota actuales basadas en el tiempo actual
Devuelve (línea, nota, progreso_nota)
)NNr8   rb   Ng      �?g�������?g      �?g�������?r8   r   ������   )r^   r9   rB   r
   r#   �	enumerater"   r   �max�min�range�len)r.   re   �gap_seconds�current_beat�current_line�line�current_note�ir)   �note_end�raw_progress�smoothing_factor�t�smoothed_progress�progress�
first_note�	last_note�
time_to_first�	next_notes                      r   �get_current_line_and_note�Song.get_current_line_and_note�   s�  � � �*�*�,���%�"� %�2�d�h�h�>��C�� ���J�J�D��z�z�)�l�h�h�.F�#����
�
�*� � � �"� �� ��!3�!3�4�G�A��z�z�D�K�K�/�H��z�z�)�l�.E�#�� !-�z�z� 9�T�[�[�H�� $'� �  �#�%� %�s�*�A�(4��>N�RU�XY�RY�>Z�8Z�([�%�!�C�'� )5�%� )5�%� �s�C��->�$?�@��#�8�;�;�I 5�P ����%�+�+�A�.�J�$�*�*�2�.�I��.�.�.� !+� 0� 0�<� ?�
� �3�&�'�S�8�8�'��s�2�2�����9�3C�3C�!C�C� $�Y��3�3� �s�<�#5�#5�6��:�;�A�'�-�-�a�0�D� ,� 2� 2�1�q�5� 9�I�#�z�z�D�K�K�7�H�#�/�L�9�?�?�4R� (�2�Y�_�_�|�5S�S�#/�s�#:�:� $0��C�#?�?� <� �T�3�&�&r   )r;   r7   N)%r   r   r   r   r   r5   r   r   r6   r7   r9   �floatr:   r;   r<   r=   r>   r?   r@   rA   r   r   r1   rB   r   r   rC   rD   rQ   r[   r^   rc   r   r   r
   r~   r   r   r   r   r3   r3       s  � �D��E�3�O��F�C���H�c���C����C����E�3�O��J����E�3�O��E�3�O��G�S���H�c���D�#�M��d�3�E�4��:�3� �I�s���K����3� �."�S� "�H!�� !�?�#� ?�%� ?�f'�e� f'��h�t�n�V^�_c�Vd�fk�Fk�@l� f'r   r3   c                   �\   � \ rS rSrSr\S\S\\   4S j5       r	\S\S\
\   4S j5       rSrg	)
�
SongLoader��   z;Clase para cargar y parsear archivos de canciones UltraStarrC   r$   c           
      �  � [         R                  R                  U 5      (       d  [        SU  S35        g/ SQnSnU H(  n [	        U SUS9 nUR                  5       nSSS5          O   UcN  [        S
U  S35         [	        U S5       nUR                  5       R                  S
SS9nSSS5        [        S5        [        5       nXl
        [         R                  R                  U 5      Ul        UR                  5       nSnU[        U5      :  Ga  Xx   (       a  Xx   R                  S5      (       Ga�  Xx   R!                  5       n	U	R                  S5      (       Ga�  ["        R$                  " SU	5      n
U
(       Gam  U
R'                  S5      R)                  5       R!                  5       nU
R'                  S5      R!                  5       nUS:X  a  X�l        GOUS:X  a  X�l        GOUS:X  a  X�l        O�US:X  a<   ["        R0                  " SSUR!                  5       5      n
 [3        U
5      S-  Ul        O�US":X  aK   ["        R0                  " S#SUR!                  5       5      n
 [3        U
5      nUS:�  a  US$:  a  US%-  nX�l        OeUS':X  a  X�l        OXUS(:X  a  X�l         OKUS):X  a  X�l!        O>US*:X  a  X�l"        O1US+:X  a  X�l#        O$US,:X  a  X�l$        OUS-:X  a   [K        U5      Ul&        US-
  nU[        U5      :  a'  Xx   (       d  GM�  Xx   R                  S5      (       a  GM�  / n[         R                  RO                  U 5      n[         R                  RO                  [         R                  R                  U 5      5      nURQ                  S!S5      nUR*                  (       d�  [        U5      S:�  a  US   Ul        O�[         R                  RS                  U5      S   RQ                  S!S5      n[        U5      S:�  a  US   Ul        O8[         R                  RS                  U5      S   Ul        URU                  S5        UR,                  (       d~  [        U5      S:�  a  US   Ul        Od[         R                  RS                  U5      S   RQ                  S!S5      n[        U5      S:�  a  US   Ul        OS/Ul        URU                  S5        UR.                  (       Gd  [         RV                  " [         R                  R                  U 5      5       Vs/ s H+  nURY                  5       R[                  S05      (       d  M)  UPM-     nnU(       a  US   Ul        O�[         R                  RS                  U5      S    S03n[         R                  R                  [         R                  R]                  [         R                  R                  U 5      U5      5      (       a  UUl        OURU                  S5        UR4                  S::  a�   [	        U SS
SS19 nUR                  5       n["        R^                  " S2U["        R`                  5      nU(       a6  UR'                  S5      R9                  SS5      n [3        U5      S-  Ul        OS3Ul        URU                  S5        SSS5        U(       a+  [        S4U  S	S5R]                  U5       35        [        S65        UR.                  (       d  [        S7U  35        gUR<                  S::  a�   [	        U SS
SS19 nUR                  5       n["        R^                  " S8U["        R`                  5      nU(       aD  UR'                  S5      R9                  SS5      n [3        U5      nUS:�  a  US$:  a  US%-  nX�l        SSS5        UR<                  S::  a  SUl        SnU[        U5      :  Ga�  Xx   R!                  5       n	US-
  nU	(       d  M0  U	R                  S95      (       a�  U	RQ                  5       n[        U5      S:�  ai   U(       a1  URb                  (       a   [K        US   5      URb                  S:   l2        [g        [K        US   5      S;9nURb                  RU                  U5        M�  U	S   S=;   a�  U	RQ                  S>S?5      n[        U5      S:�  a�   US   n[K        US   5      n[K        US   5      n[K        US?   RQ                  S>5      S   5      nS>R]                  US?   RQ                  S>5      SS 5      nU(       d$  [g        US;9nURb                  RU                  U5        [i        UUUUUS@9n URj                  RU                  U 5        [m        URd                  UU-   5      Ul2        GM�  U[        U5      :  a  GM�  URb                  (       d  [        SBU  35        gU$ ! , (       d  f       G	Np= f! [         a     G	M�  [         a   n[        SU  SU S	U 35         SnAG	M�  SnAff = f! , (       d  f       G	Nr= f! [         a  n[        SU  S	U 35         SnAgSnAff = f! [6         a)    U
R9                  SS5      n[3        U5      S-  Ul         GN�f = f! [         a%  n[        S U S![;        U5       35         SnAGN�SnAff = f! [6         a    [3        U
R9                  SS5      5      n GNrf = f! [         a%  n[        S&U S![;        U5       35         SnAGN#SnAff = f! [6         a    [        S.U 35         GNFf = fs  snf ! [6         a    S3Ul        URU                  S5         GN�f = f! , (       d  f       GN�= f! [         a    S3Ul        URU                  S5         GN�f = f! [6         a     GNf = f! , (       d  f       GN= f! [         a     GN'f = f! [6         a    [        S<U	 35         GN_f = f! [6         a    [        SAU	 35         GNyf = f)Cu(   Carga una canción desde un archivo .txtzError: El archivo �
 no existeN)�utf-8zlatin-1�cp1252z
iso-8859-1�r)�encodingzError al leer el archivo u    con codificación z: z"Error: No se pudo leer el archivo u#    con ninguna codificación conocida�rbr�   �ignore)�errorsu4   Archivo leído en modo binario con errores ignoradoszError fatal al leer el archivo r   �#z#\s*([^:]+)\s*:(.*)rh   �   �TITLE�ARTIST�MP3�BPMz[^\d.,]r&   �   �,�.zError al parsear BPM: � - �GAPz	[^\d.,\-]�d   i�  zError al parsear GAP: �COVER�
BACKGROUND�VIDEO�GENRE�EDITION�LANGUAGE�YEARzError al parsear YEAR: �DesconocidorF   )r�   r�   z#\s*BPM\s*:\s*(\d+[.,]?\d*)g      n@z!Advertencia: Campos faltantes en z, z*Usando valores derivados o predeterminadosz0Error: No se pudo encontrar un archivo MP3 para z#\s*GAP\s*:\s*(\d+[.,]?\d*)�-rg   )r
   u    Error al parsear fin de línea: )�:�*�F�R�G� �   )r   r
   r   r   r   zError al parsear nota: u$   Error: No se encontraron líneas en )7rG   rH   rI   �print�open�read�UnicodeDecodeErrorrM   �decoder3   rC   �dirnamerD   �
splitlinesrm   �
startswith�strip�re�match�group�upperr5   r6   r7   �subr�   r9   �
ValueError�replacer   r:   r;   r<   r=   r>   r?   r@   r   rA   �basename�split�splitext�appendrJ   rK   rL   r,   �search�
IGNORECASErB   r#   r   r
   r"   rj   )!rC   �	encodings�contentr�   rO   �e�songrB   rs   rq   �header_match�header_name�header_value�
cleaned_value�	bpm_value�	gap_value�missing_fields�	file_name�folder_name�folder_parts�
file_partsrP   �
potential_mp3�	bpm_match�	gap_matchrp   �parts�	note_typer
   r   r   r   r)   s!                                    r   �	load_song�SongLoader.load_song�   s�
  � � �w�w�~�~�i�(�(��&�y�k��<�=�� A�	��� "�H�
a��)�S�8�<���f�f�h�G� =� � "� �?��6�y�k�Ad�e�f�
��)�T�*�a��f�f�h�o�o�g�h�o�G�G� +��L�N� �v��"���7�7�?�?�9�5��� �"�"�$�� 
���#�e�*�n�e�h�%�(�2E�2E�c�2J�2J��8�>�>�#�D����s�#�#�  "�x�x�(>��E���".�"4�"4�Q�"7�"=�"=�"?�"E�"E�"G�K�#/�#5�#5�a�#8�#>�#>�#@�L�"�g�-�%1�
�$��0�&2��$��-�(4�
�$��-�V�,.�F�F�:�r�<�CU�CU�CW�,X�M�@�+0��+?�!�+C��� %��-�V�,.�F�F�<��\�EW�EW�EY�,Z�M�S�,1�-�,@�	�  )�1�}��S�� )�T� 1�	�'0�H� %��/�%1�
�$��4�*6��$��/�%1�
�$��/�%1�
�$�	�1�'3��$�
�2�(4�
�$��.�L�(+�L�(9�D�I� 
��F�A�M �#�e�*�n�e�h�h�%�(�2E�2E�c�2J�2J�R �� �G�G�$�$�Y�/�	��g�g�&�&�r�w�w���y�'A�B�� #�(�(���2���z�z��<� �1�$�)�!�_��
�  �W�W�-�-�i�8��;�A�A�%��K�
��z�?�Q�&�!+�A��D�J�!#���!1�!1�)�!<�Q�!?�D�J�"�)�)�'�2��{�{��<� �1�$�*�1�o���  �W�W�-�-�i�8��;�A�A�%��K�
��z�?�Q�&�",�Q�-�D�K�"/�D�K�"�)�)�(�3��}�}�}�$&�J�J�r�w�w���y�/I�$J� 7�$J�q��7�7�9�-�-�f�5� �$J�I� 7� � )�!���
�
 $&�7�7�#3�#3�I�#>�q�#A�"B�$� G�
��7�7�>�>�"�'�'�,�,�r�w�w���y�/I�=�"Y�Z�Z�$1�D�M�"�)�)�%�0��8�8�q�=�
-��)�S�7�8�L�PQ��f�f�h�G� "�	�	�*H�'�SU�S`�S`� a�I� �$-�O�O�A�$6�$>�$>�s�C�$H�	�9�',�Y�'7�!�';�D�H�
 $)���&�-�-�e�4� M�$ ��5�i�[��4�9�9�^�C\�B]�^�_��>�@� �}�}��D�Y�K�P�Q�� �8�8�q�=�
��)�S�7�8�L�PQ��f�f�h�G� "�	�	�*H�'�SU�S`�S`� a�I� �$-�O�O�A�$6�$>�$>�s�C�$H�	�!�(-�i�(8�I�(�1�}��S�� )�T� 1�	�'0�H� M�$ �x�x�1�}� ��� ���#�e�*�n��8�>�>�#�D�
��F�A��� ���s�#�#��
�
����u�:��?�	I�'�D�J�J�14�U�1�X��D�J�J�r�N�.� (,�#�e�A�h�-�'@���
�
�)�)�,�7� � �A�w�3�3��
�
�3��*���u�:��?�@�$)�!�H�	� #�E�!�H�
��!$�U�1�X��� #�E�!�H�N�N�3�$7��$:� ;��"�x�x��a����s�(;�A�B�(?�@��  ,�+/�e�+<�L� �J�J�-�-�l�;�  $��%��V[�bf�g��$�*�*�1�1�$�7� ,/�|�/?�/?����+P��(� �_ �#�e�*�n�d �z�z��8���D�E�� ��i =�<�� &� 
��� 
a��1�)��<O�PX�z�Y[�\]�[^�_�`�`��
a�� +�*�� � 
��7�	�{�"�Q�C�H�I���
��L $.� @�,9�,A�,A�#�s�,K�	�+0��+;�a�+?���@��  )� V�!�$:�<�.��C�PQ�F�8�"T�U�U��V�� $.� S�,1�-�2G�2G��S�2Q�,R�	�S��  )� V�!�$:�<�.��C�PQ�F�8�"T�U�U��V��"  *� L�!�$;�L�>�"J�K�L��V7��2  *� 9�',�D�H�*�1�1�%�8�9�� M�L�� � 
-� ����%�%�e�,�
-��<  *� !� �!�� M�L�� � 
��
��> &� I�� @���G�H�I��4 &� @�� 7��v�>�?�@�s�  �g�g�g�<h$ �h�'h$ �6&j �i �8&k  �j4 �*k  �l �(l1�l1�
m1 �Am�6l6�	m�"m1 �
n= �An+�9"n�n= �"A(o �7Co- �
g	�g�
h�&	h�/h
�
h�
h!�h$ �$
i	�.i�i	�/i?�;j �>i?�?j �
j1�j,�,j1�4%k�k  �k�k  � 
l�*l
�
l�l.�-l.�6"m�m�m�m�
m.�)m1 �.m1 �1"n�n�
n(�$n+�'n(�(n+�+
n:�5n= �:n= �=
o�
o�o*�)o*�-p	�p	�	directoryc                 �D  � / nSnSn[         R                  R                  U 5      (       d  [        SU  S35        U$ [        SU  S35        [         R                  " U 5       H�  u  pEnU H�  nUR                  5       R
                  S5      (       d  M)  US-
  n[         R                  R                  XG5      n[        R                  U5      n	U	(       a  UR                  U	5        M|  US-
  nM�     M�     [        S[        U5       S	U 35        US:�  a  [        S
U 35        U$ )z*Carga todas las canciones de un directorior   zError: El directorio r�   zCargando canciones desde: z...z.txtrh   zCanciones cargadas: �/zCanciones con errores: )rG   rH   rI   r�   �walkrK   rL   r,   r�   r�   r�   rm   )
r�   �songs�error_count�total_files�root�_�files�filerC   r�   s
             r   �load_songs_from_directory�$SongLoader.load_songs_from_directory  s  � � �������w�w�~�~�i�(�(��)�)��J�?�@��L� 	�*�9�+�S�9�:� !�g�g�i�0�N�D�U����:�:�<�(�(��0�0��1�$�K� "�����T� 8�I�%�/�/�	�:�D�����T�*�#�q�(�� � 1� 	�$�S��Z�L��+��?�@���?��+�K�=�9�:��r   r   N)
r   r   r   r   r   �staticmethodr   r   r3   r�   r   r�   r   r   r   r   r�   r�   �   sT   � �E��a�S� a�X�d�^� a� �a�F	 ��S� �T�$�Z� � �r   r�   �__main__rh   u
   Canción: r�   zBPM: z, GAP: z
Archivo MP3: u	   Líneas: u   Primera línea: u   Notas en la primera línea: z0Uso: python song_loader.py <ruta_al_archivo_txt>)rG   r�   �dataclassesr   r   �typingr   r   r   r   r
   r   r3   r�   r   �sysrm   �argvrC   r�   r�   r�   r6   r5   r9   r:   rQ   rB   rq   r/   r"   r   r   r   �<module>r�      sz  �� 
� 	� (� .� .� �� � �� �9� 9� �9� �E'� E'� �E'�PF� F�R
 �z���
�3�8�8�}�q���H�H�Q�K�	��#�#�I�.����J�t�{�{�m�3�t�z�z�l�;�<��E�$�(�(��7�4�8�8�*�5�6��M�$�"8�"8�":�!;�<�=��I�c�$�*�*�o�.�/�0� �z�z��z�z�!�}���(�����(9�:�;��4�S����_�4E�F�G� � � 	�@�A�' r   
