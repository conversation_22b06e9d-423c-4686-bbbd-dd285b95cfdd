�

    ~hh�  �                   �B   � S SK JrJrJrJr  S SKJrJr   " S S\5      rg)�    )�QUrl�QTimer�
pyqtSignal�QObject)�QMediaPlayer�
QMediaContentc                   ��   ^ � \ rS rSrSr\" \5      r\" \5      r	\" \5      r
U 4S jrS\4S jr
S rS rS rS	\4S
 jrS\4S jrS\4S
 jrS\4S jrS	\4S jrS\4S jrS\4S jrS rSrU =r$ )�AudioPlayer�   z'Clase para reproducir archivos de audioc                 �D  >� [         TU ]  5         [        5       U l        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        [        5       U l        U R                  R                  S5        U R                  R                  R                  U R                  5        SU l        SU l        SU l        SU l        g )N�
   r   T�        )�super�__init__r   �player�positionChanged�connect�_on_position_changed�stateChanged�_on_state_changed�durationChanged�_on_duration_changedr   �update_timer�setInterval�timeout�_emit_position�last_position_update�interpolation_enabled�duration�position)�self�	__class__s    ��^   C:\Users\<USER>\Desktop\Bacup\Widget_def_versión_1\ultrastar_basics\src\core\audio_player.pyr   �AudioPlayer.__init__   s�   �� �
���� #�n��� 	
���#�#�+�+�D�,E�,E�F���� � �(�(��)?�)?�@����#�#�+�+�D�,E�,E�F� #�H������%�%�b�)����!�!�)�)�$�*=�*=�>� %&��!�%)��"� ��
���
�    �	file_pathc                 �  � U R                  5         SU l        SU l        U R                  R	                  [        [        R                  " U5      5      5        U R                  R                  S5        U R                  R                  S5        g)zCarga un archivo de audior   N)�stopr    r   r   �setMediar   r   �
fromLocalFiler   �emitr   )r!   r&   s     r#   �load�AudioPlayer.load(   sk   � � 	
�	�	�� ��
���
� 	
�����]�4�+=�+=�i�+H�I�J� 	
���!�!�#�&����!�!�#�&r%   c                 ��   � U R                   R                  S5        SU l        U R                   R                  5         U R                  R                  5         U R                  R                  S5        g)zReproduce el audior   r   N)r   �setPositionr    �playr   �startr   r+   �r!   s    r#   r0   �AudioPlayer.play8   sW   � � 	
������"���
� 	
�����������!� 	
���!�!�#�&r%   c                 �l   � U R                   R                  5         U R                  R                  5         g)u   Pausa la reproducciónN)r   �pauser   r(   r2   s    r#   r5   �AudioPlayer.pauseE   s$   � ������������ r%   c                 �l   � U R                   R                  5         U R                  R                  5         g)u   Detiene la reproducciónN)r   r(   r   r2   s    r#   r(   �AudioPlayer.stopJ   s$   � ������������ r%   r    c                 �R   � U R                   R                  [        US-  5      5        g)u"   Establece la posición en segundosi�  N)r   r/   �int�r!   r    s     r#   �set_position�AudioPlayer.set_positionO   s   � �������H�t�O� 4�5r%   �returnc                 �   � U R                   $ )u'   Obtiene la posición actual en segundos)r    r2   s    r#   �get_position�AudioPlayer.get_positionS   �   � ��}�}�r%   c                 �   � U R                   $ )u    Obtiene la duración en segundos)r   r2   s    r#   �get_duration�AudioPlayer.get_durationW   rB   r%   c                 �X   � U R                   R                  5       [        R                  :H  $ )u,   Comprueba si el audio se está reproduciendo)r   �stater   �PlayingStater2   s    r#   �
is_playing�AudioPlayer.is_playing[   s    � ��{�{� � �"�l�&?�&?�?�?r%   c                 �b   � US-  U l         U R                  R                  U R                   5        g)u)   Manejador para cuando cambia la posición�     @�@N)r    r   r+   r;   s     r#   r   � AudioPlayer._on_position_changed_   �&   � � �6�)��
����!�!�$�-�-�0r%   rG   c                 ��   � U R                   R                  U5        U[        R                  :X  a  U R                  R                  5         gU R                  R
                  5         g)z&Manejador para cuando cambia el estadoN)r   r+   r   rH   r   r1   r(   )r!   rG   s     r#   r   �AudioPlayer._on_state_changedd   sJ   � ������u�%� �L�-�-�-����#�#�%����"�"�$r%   r   c                 �b   � US-  U l         U R                  R                  U R                   5        g)u)   Manejador para cuando cambia la duraciónrL   N)r   r   r+   )r!   r   s     r#   r   � AudioPlayer._on_duration_changedn   rN   r%   c                 ��  � U R                   R                  5       S-  nU R                  (       a�  U R                  5       (       ap  U R                  R                  5       S-  nU R                  U-   n[
        X-
  5      S:�  a  Xl        OX0l        U R                  R                  U R                  5        gXR                  :w  a,  Xl        U R                  R                  U R                  5        gg)uI   Emite la posición actual (llamado por el timer) con interpolación suaverL   g�������?N)	r   r    r   rI   r   �interval�absr   r+   )r!   �actual_position�current_time�interpolated_positions       r#   r   �AudioPlayer._emit_positions   s�   � � �+�+�.�.�0�6�9���%�%�$�/�/�*;�*;��,�,�5�5�7�&�@�L� %)�M�M�L�$@�!� �?�:�;�c�A� /�
� 5�
� 
� � �%�%�d�m�m�4� �-�-�/� /�
��$�$�)�)�$�-�-�8� 0r%   )r   r   r   r   r    r   )�__name__�
__module__�__qualname__�__firstlineno__�__doc__r   �floatr   r:   r   r   r   �strr,   r0   r5   r(   r<   r@   rD   �boolrI   r   r   r   r   �__static_attributes__�
__classcell__)r"   s   @r#   r
   r
      s�   �� �1� !��'�O��c�?�L� ��'�O��0'�c� '� '�!�
!�
6�U� 6��e� ��e� �@�D� @�1�S� 1�
%�s� %�1�S� 1�
9� 9r%   r
   N)	�PyQt5.QtCorer   r   r   r   �PyQt5.QtMultimediar   r   r
   � r%   r#   �<module>rg      s   �� ;� :� :�E9�'� E9r%   
