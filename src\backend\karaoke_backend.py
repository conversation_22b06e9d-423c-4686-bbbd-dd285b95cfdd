#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import QObject, Signal, Property, Slot
from PySide6.QtGui import QColor

from core.karaoke_engine import KaraokeEngine
from core.song_loader import Song


class KaraokeBackend(QObject):
    """Karaoke backend for QML interface"""

    # Signals
    textChanged = Signal()
    effectChanged = Signal()
    colorsChanged = Signal()
    settingsChanged = Signal()

    # Effect constants
    EFFECT_SIMPLE = 0
    EFFECT_ZOOM = 1
    EFFECT_PARTICLE = 2
    EFFECT_BALL = 3
    EFFECT_SHIFT = 4
    EFFECT_WAVE = 5
    EFFECT_PULSE = 6
    EFFECT_TYPEWRITER = 7
    EFFECT_AEGISUB = 8
    EFFECT_YTMUSIC = 9

    def __init__(self):
        super().__init__()

        # Karaoke engine
        self.engine = KaraokeEngine()

        # Current effect
        self._current_effect = self.EFFECT_SIMPLE

        # Colors
        self._sung_color = QColor(0, 255, 0)  # Green for sung text
        self._pending_color = QColor(255, 255, 255)  # White for pending text
        self._next_line_color = QColor(128, 128, 128)  # Gray for next line
        self._ball_color = QColor(255, 255, 0)  # Yellow for ball

        # Settings
        self._transition_effect = True
        self._interpolation = True
        self._transition_speed = 6.0
        self._sync_offset = 0.0
        self._smoothing_factor = 0.5
        self._anticipation_time = 0.022

    def set_song(self, song: Song):
        """Set current song"""
        self.engine.set_song(song)
        self.textChanged.emit()

    def update_time(self, current_time: float):
        """Update current time"""
        self.engine.update(current_time)
        self.textChanged.emit()

    @Property(str, notify=textChanged)
    def currentLineText(self):
        """Return current line text"""
        return self.engine.get_current_line_text()

    @Property(str, notify=textChanged)
    def nextLineText(self):
        """Return next line text"""
        return self.engine.get_next_line_text()

    @Property(str, notify=textChanged)
    def sungText(self):
        """Return sung text"""
        sung, _, _ = self.engine.get_highlighted_text()
        return sung

    @Property(str, notify=textChanged)
    def currentSyllable(self):
        """Return current syllable"""
        _, current, _ = self.engine.get_highlighted_text()
        return current

    @Property(str, notify=textChanged)
    def pendingText(self):
        """Return pending text"""
        _, _, pending = self.engine.get_highlighted_text()
        return pending

    @Property(float, notify=textChanged)
    def syllableProgress(self):
        """Return syllable progress (0.0 - 1.0)"""
        return self.engine.get_syllable_progress()

    @Property(int, notify=effectChanged)
    def currentEffect(self):
        """Return current effect"""
        return self._current_effect

    @currentEffect.setter
    def currentEffect(self, effect):
        """Set current effect"""
        if self._current_effect != effect:
            self._current_effect = effect
            self.effectChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def sungColor(self):
        """Return sung text color"""
        return self._sung_color

    @sungColor.setter
    def sungColor(self, color):
        """Set sung text color"""
        if self._sung_color != color:
            self._sung_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def pendingColor(self):
        """Return pending text color"""
        return self._pending_color

    @pendingColor.setter
    def pendingColor(self, color):
        """Set pending text color"""
        if self._pending_color != color:
            self._pending_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def nextLineColor(self):
        """Return next line color"""
        return self._next_line_color

    @nextLineColor.setter
    def nextLineColor(self, color):
        """Set next line color"""
        if self._next_line_color != color:
            self._next_line_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def ballColor(self):
        """Return ball color"""
        return self._ball_color

    @ballColor.setter
    def ballColor(self, color):
        """Set ball color"""
        if self._ball_color != color:
            self._ball_color = color
            self.colorsChanged.emit()

    @Property(bool, notify=settingsChanged)
    def transitionEffect(self):
        """Return transition effect setting"""
        return self._transition_effect

    @transitionEffect.setter
    def transitionEffect(self, enabled):
        """Set transition effect"""
        if self._transition_effect != enabled:
            self._transition_effect = enabled
            self.settingsChanged.emit()

    @Property(bool, notify=settingsChanged)
    def interpolation(self):
        """Return interpolation setting"""
        return self._interpolation

    @interpolation.setter
    def interpolation(self, enabled):
        """Set interpolation"""
        if self._interpolation != enabled:
            self._interpolation = enabled
            if enabled:
                self.engine.transition_speed = self._transition_speed
            else:
                self.engine.transition_speed = 100.0
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def transitionSpeed(self):
        """Return transition speed"""
        return self._transition_speed

    @transitionSpeed.setter
    def transitionSpeed(self, speed):
        """Set transition speed"""
        if self._transition_speed != speed:
            self._transition_speed = speed
            if self._interpolation:
                self.engine.transition_speed = speed
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def syncOffset(self):
        """Return sync offset"""
        return self._sync_offset

    @syncOffset.setter
    def syncOffset(self, offset):
        """Set sync offset"""
        if self._sync_offset != offset:
            self._sync_offset = offset
            self.engine.sync_offset = offset
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def smoothingFactor(self):
        """Return smoothing factor"""
        return self._smoothing_factor

    @smoothingFactor.setter
    def smoothingFactor(self, factor):
        """Set smoothing factor"""
        if self._smoothing_factor != factor:
            self._smoothing_factor = factor
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def anticipationTime(self):
        """Return anticipation time"""
        return self._anticipation_time

    @anticipationTime.setter
    def anticipationTime(self, time):
        """Set anticipation time"""
        if self._anticipation_time != time:
            self._anticipation_time = time
            self.engine.anticipation_time = time
            self.settingsChanged.emit()

    @Slot(int)
    def setEffect(self, effect):
        """Set karaoke effect"""
        self.currentEffect = effect

    @Slot(result=str)
    def getEffectName(self):
        """Get current effect name"""
        effect_names = [
            "Simple", "Zoom", "Particle", "Ball", "Shift", "Wave", "Pulse", "Typewriter", "Aegisub 3D"
        ]
        if 0 <= self._current_effect < len(effect_names):
            return effect_names[self._current_effect]
        return "Unknown"

    @Slot(result=str)
    def getDebugInfo(self):
        """Get debug information"""
        if not self.engine.song:
            return "No song loaded"

        song = self.engine.song
        gap_seconds = song.get_gap_seconds()

        info = f"Detailed song information:\n\n"
        info += f"Title: {song.title}\n"
        info += f"Artist: {song.artist}\n"
        info += f"BPM: {song.bpm/4:.2f} (internal value: {song.bpm})\n"
        info += f"GAP: {song.gap} ms ({gap_seconds:.2f} s)\n"
        info += f"Genre: {song.genre}\n"
        info += f"Year: {song.year}\n"
        info += f"MP3 file: {song.mp3_file}\n"
        info += f"Full MP3 path: {song.get_full_mp3_path()}\n"
        info += f"Cover: {song.cover}\n"
        info += f"Number of lines: {len(song.lines)}\n"
        info += f"Current karaoke effect: {self.getEffectName()}\n"

        if song.lines:
            first_line = song.lines[0]
            last_line = song.lines[-1]
            info += f"\nFirst line: {first_line.get_text()}\n"
            info += f"First line start time: {first_line.start} beats ({song.get_beat_time(first_line.start):.2f} s)\n"
            info += f"Last line: {last_line.get_text()}\n"
            info += f"Last line end time: {last_line.end} beats ({song.get_beat_time(last_line.end):.2f} s)\n"

            # Show information about first notes
            if first_line.notes:
                first_note = first_line.notes[0]
                info += f"\nFirst note: {first_note.text}\n"
                info += f"First note start time: {first_note.start} beats ({song.get_beat_time(first_note.start):.2f} s)\n"
                info += f"First note duration: {first_note.length} beats ({first_note.length * 60 / song.bpm:.2f} s)\n"

        return info
