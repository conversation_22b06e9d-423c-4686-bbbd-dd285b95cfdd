�

    ��hx$  �                   �@   � S SK JrJrJr  S SKJrJrJr   " S S5      rg)�    )�List�Tuple�Optional)�Song�Line�Notec                   �   � \ rS rSrSrSS\4S jjrS\4S jrS\4S jr	S	\
4S
 jrS	\\
\
\
4   4S jr
S	\
4S jrS	\4S
 jrS	\4S jrS	\4S jrSrg)�
KaraokeEngine�   z,Motor para el resaltado de letras de karaokeN�songc                 �   � Xl         SU l        S U l        S U l        SU l        S U l        SU l        SU l        SU l        SU l	        SU l
        g )N�        g      @g�I+��?)r   �current_time�current_line�current_note�
note_progress�	last_note�target_progress�last_update_time�transition_speed�sync_offset�anticipation_time��selfr   s     �`   C:\Users\<USER>\Desktop\Bacup\Widget_def_versión_1\ultrastar_basics\src\core\karaoke_engine.py�__init__�KaraokeEngine.__init__   sZ   � ��	���� ��� ��� ������  #��� #��� #��� ���!&���    c                 �r   � Xl         SU l        SU l        SU l        SU l        SU l        SU l        SU l        g)u   Establece la canción actualr   N)r   r   r   r   r   r   r   r   r   s     r   �set_song�KaraokeEngine.set_song   s@   � ��	���� ��� ��� ������  #��� #��r   r   c                 �  � U R                   (       d  gU R                   R                  5       nX:  a  Xl        SU l        SU l        SU l        gXl        XR                  -   nU R                   R                  U5      u  U l        U l        nXR                  -
  nXl        U R                  S:X  d  U R                  U R                  :w  a  X@l	        X@l        U R                  U l
        gUS:�  a  X@R                  -
  U-  OSnX@l	        UnU R                  (       a=  US:�  a7  U R                  nUS:�  a  US-  n	OUS:  a  US-  n	OUn	[        SXFU	-  -   5      n[        U R
                  U-
  5      S:�  a�  [        U R
                  U-
  5      n
U R                  U-  SU
S	-  -   -  nU R                  U-  S
-  n[        X�5      n
[        U
S5      nXpR
                  :�  a  [        U R
                  U-   U5      U l        O6U R                  U R                  :w  a  Xpl        OXpR
                  :�  a  Xpl        U R                  U l
        g)zx
Actualiza el estado del motor de karaoke basado en el tiempo actual

Args:
    current_time: Tiempo actual en segundos
Nr   r   �      �?g�������?g      �?g�������?g����MbP?g      @g      �?g�������?)r   �get_gap_secondsr   r   r   r   r   �get_current_line_and_noter   r   r   r   �min�absr   �max)r   r   �gap_seconds�
adjusted_timer   �dt�
progress_rate�predictive_target�base_prediction_time�prediction_time�diff�adaptive_speed�
base_speed�combined_speed�speeds                  r   �update�KaraokeEngine.update+   sU  � � �y�y�� �i�i�/�/�1���%� ,�� $�D�� $�D��!$�D���(�� %�'7�'7�7�
� AE�	�	�@c�@c�dq�@r�=���4�,�o� �1�1�
1�� ,�� ���3�&�$�*;�*;�t�~�~�*M�#2� �!0��!�.�.�D�N�� JL�a���+?�+?�?�2�E�UV�
�  /�� ,�� �����!2�#'�#9�#9� � �s�"�"6��"<����$�"6��"<�� #7�� !$�C��?�;Z�)Z� [�� �t�!�!�$5�5�6��>� �t�)�)�,=�=�>�D� "�2�2�R�7�3����;K�L�N� �.�.��3�c�9�J� !��<�N� ���,�E� !�#5�#5�5�%(��);�);�e�)C�EV�%W��"� �$�$����6�):�&�� !�$6�$6�6�%6�"� �*�*��r   �returnc                 �Z   � U R                   (       d  gU R                   R                  5       $ )u.   Devuelve el texto completo de la línea actual� )r   �get_text�r   s    r   �get_current_line_text�#KaraokeEngine.get_current_line_text�   s#   � �� � ��� � �)�)�+�+r   c                 �V  � U R                   (       a  U R                  (       d  SSU R                  5       4$ SnU R                  R                  nSnSnU R                   R                   H;  nXPR                  :X  a  SnM  U(       d  XR                  -
  nM-  X5R                  -
  nM=     XU4$ )u�   
Devuelve el texto dividido en tres partes:
1. Texto ya cantado en la línea actual
2. Texto de la sílaba actual
3. Texto pendiente de cantar en la línea actual
r9   FT)r   r   r<   �text�notes)r   �	sung_text�current_syllable�pending_text�
found_current�notes         r   �get_highlighted_text�"KaraokeEngine.get_highlighted_text�   s�   � � � � ��(9�(9��r�4�5�5�7�7�7� �	��,�,�1�1�����
��%�%�+�+�D��(�(�(� $�
�"��Y�Y�&�	��	�	�)��
 ,� �L�8�8r   c                 ��   � U R                   (       a  U R                  (       d  gSnU R                   R                   H/  nU(       a  UR                  5       s  $ X R                  :X  d  M-  SnM1     g)u(   Devuelve el texto de la siguiente línear9   FT)r   r   �linesr:   �r   rD   �lines      r   �get_next_line_text� KaraokeEngine.get_next_line_text�   sR   � ��y�y�� 1� 1�� �
��I�I�O�O�D���}�}��&��(�(�(� $�
�	 $� r   c                 �   � U R                   $ )ub   
Devuelve el progreso de la sílaba actual (0.0 - 1.0)
Útil para efectos de resaltado progresivo
)r   r;   s    r   �get_syllable_progress�#KaraokeEngine.get_syllable_progress�   s   � �
 �!�!�!r   c                 �  � U R                   (       d  gU R                  R                  U R                   R                  5      U R                  R                  U R                   R                  5      -
  nUS::  a  gU R
                  U R                  R                  U R                   R                  5      -
  n[
        S[        SX!-  5      5      $ )u6   
Devuelve el progreso de la línea actual (0.0 - 1.0)
r   r   r#   )r   r   �
get_beat_time�end�startr   r(   r&   )r   �
line_duration�current_positions      r   �get_line_progress�KaraokeEngine.get_line_progress�   s�   � � � � ���	�	�/�/��0A�0A�0E�0E�F����I`�I`�ae�ar�ar�ax�ax�Iy�y�
��A����,�,�t�y�y�/F�/F�t�GX�GX�G^�G^�/_�_���3��C�!1�!A�B�C�Cr   c                 �$  � U R                   (       a  U R                  (       d  gSnU R                   R                   HQ  nU(       a4  U R                   R                  UR                  5      U R
                  -
  s  $ X R                  :X  d  MO  SnMS     g)ua   
Devuelve el tiempo en segundos hasta la próxima línea
Útil para mostrar una cuenta regresiva
r   FT)r   r   rI   rR   rT   r   rJ   s      r   �get_countdown_to_next_line�(KaraokeEngine.get_countdown_to_next_line�   sl   � �
 �y�y�� 1� 1�� �
��I�I�O�O�D���y�y�.�.�t�z�z�:�T�=N�=N�N�N��(�(�(� $�
�	 $� r   )r   r   r   r   r   r   r   r   r   r   r   )N)�__name__�
__module__�__qualname__�__firstlineno__�__doc__r   r   r    �floatr5   �strr<   r   rF   rL   rO   rW   rZ   �__static_attributes__� r   r   r
   r
      s�   � �6�'�T� '�"$�T� $�j+�5� j+�X,�s� ,�9�e�C��c�M�&:� 9�8
�C� 
�"�u� "�D�5� D��E� r   r
   N)	�typingr   r   r   �src.core.song_loaderr   r   r   r
   rd   r   r   �<module>rg      s   �� )� (� 1� 1�e� er   
