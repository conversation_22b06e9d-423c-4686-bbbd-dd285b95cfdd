#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, <PERSON><PERSON>, Optional
from core.song_loader import Song, Line, Note


class KaraokeEngine:
    """Karaoke lyrics highlighting engine"""

    def __init__(self, song: Song = None):
        self.song = song
        self.current_time = 0.0
        self.current_line = None
        self.current_note = None
        self.note_progress = 0.0
        self.last_note = None  # To detect note changes

        # Variables for animation smoothing
        self.target_progress = 0.0  # Target progress
        self.last_update_time = 0.0  # Time of last update
        self.transition_speed = 6.0  # Transition speed (higher = faster)

        # Variables for synchronization
        self.sync_offset = 0.0  # Sync offset in seconds
        self.anticipation_time = 0.022  # Anticipation time in seconds (22ms default)

    def set_song(self, song: Song):
        """Set current song"""
        self.song = song
        self.current_time = 0.0
        self.current_line = None
        self.current_note = None
        self.note_progress = 0.0
        self.last_note = None

        # Reset animation variables
        self.target_progress = 0.0
        self.last_update_time = 0.0

        # Don't reset sync offset to maintain user configuration

    def update(self, current_time: float):
        """
        Update karaoke engine state based on current time

        Args:
            current_time: Current time in seconds
        """
        if not self.song:
            return

        # Check if we're before the initial GAP
        # If so, don't show anything (keep everything in initial state)
        gap_seconds = self.song.get_gap_seconds()
        if current_time < gap_seconds:
            self.current_time = current_time
            self.current_line = None
            self.current_note = None
            self.note_progress = 0.0
            return

        self.current_time = current_time

        # Apply sync offset
        adjusted_time = current_time + self.sync_offset

        # Get current line, note and progress with adjusted time
        self.current_line, self.current_note, target_progress = self.song.get_current_line_and_note(adjusted_time)

        # Calculate elapsed time since last update
        dt = current_time - self.last_update_time
        self.last_update_time = current_time

        # If first update or note change, set progress directly
        if self.target_progress == 0.0 or self.current_note != self.last_note:
            self.target_progress = target_progress
            self.note_progress = target_progress
            self.last_note = self.current_note
            return

        # Calculate target progress change rate
        # This helps us predict where progress will be in the future
        progress_rate = (target_progress - self.target_progress) / dt if dt > 0 else 0

        # Update target progress
        self.target_progress = target_progress

        # Calculate predictive target progress that anticipates movement
        # This helps better synchronize animation with audio
        predictive_target = target_progress

        # If we're in an active note and progress is increasing
        if self.current_note and progress_rate > 0:
            # Use configurable anticipation time
            base_prediction_time = self.anticipation_time

            # Adjust prediction time based on change speed
            # If progress changes rapidly, we need less anticipation
            if progress_rate > 1.0:
                # For rapid changes, reduce anticipation
                prediction_time = base_prediction_time * 0.8
            elif progress_rate < 0.5:
                # For slow changes, slightly increase anticipation
                prediction_time = base_prediction_time * 1.1
            else:
                # For normal changes, use base time
                prediction_time = base_prediction_time

            # Calculate predictive progress
            predictive_target = min(1.0, target_progress + progress_rate * prediction_time)

        # Smoothly interpolate towards target progress
        if abs(self.note_progress - predictive_target) > 0.001:
            # Calculate interpolation speed based on elapsed time
            # Adjust speed based on difference for better synchronization
            diff = abs(self.note_progress - predictive_target)

            # Adaptive speed: faster when we're further from target
            # Increase acceleration factor to reduce lag
            adaptive_speed = self.transition_speed * dt * (1.0 + diff * 3.0)

            # Increase base speed to reduce lag
            base_speed = self.transition_speed * dt * 1.5

            # Combine base and adaptive speed
            combined_speed = max(base_speed, adaptive_speed)

            # Limit speed to avoid abrupt jumps
            # Increase limit to allow faster updates
            speed = min(combined_speed, 0.2)

            # Interpolate towards target, but NEVER go backwards
            if predictive_target > self.note_progress:
                # Only advance, never go backwards
                self.note_progress = min(self.note_progress + speed, predictive_target)
            else:
                # If target is behind us, maintain current progress
                # unless it's a note change
                if self.current_note != self.last_note:
                    self.note_progress = predictive_target
        else:
            # If we're very close to target, set it directly
            # but only if it doesn't involve going backwards
            if predictive_target >= self.note_progress:
                self.note_progress = predictive_target

        # Save current note for next update
        self.last_note = self.current_note

    def get_current_line_text(self) -> str:
        """Return complete text of current line"""
        if not self.current_line:
            return ""
        return self.current_line.get_text()

    def get_highlighted_text(self) -> Tuple[str, str, str]:
        """
        Return text divided into three parts:
        1. Already sung text in current line
        2. Current syllable text
        3. Pending text in current line
        """
        if not self.current_line or not self.current_note:
            return "", "", self.get_current_line_text()

        # Already sung text (notes before current)
        sung_text = ""
        # Current syllable text
        current_syllable = self.current_note.text
        # Pending text (notes after current)
        pending_text = ""

        found_current = False
        for note in self.current_line.notes:
            if note == self.current_note:
                found_current = True
            elif not found_current:
                sung_text += note.text
            else:
                pending_text += note.text

        return sung_text, current_syllable, pending_text

    def get_next_line_text(self) -> str:
        """Return text of next line"""
        if not self.song or not self.current_line:
            return ""

        # Find next line
        found_current = False
        for line in self.song.lines:
            if found_current:
                return line.get_text()
            if line == self.current_line:
                found_current = True

        return ""

    def get_syllable_progress(self) -> float:
        """
        Return progress of current syllable (0.0 - 1.0)
        Useful for progressive highlighting effects
        """
        return self.note_progress

    def get_line_progress(self) -> float:
        """
        Return progress of current line (0.0 - 1.0)
        """
        if not self.current_line:
            return 0.0

        line_duration = self.song.get_beat_time(self.current_line.end) - self.song.get_beat_time(self.current_line.start)
        if line_duration <= 0:
            return 0.0

        current_position = self.current_time - self.song.get_beat_time(self.current_line.start)
        return max(0.0, min(1.0, current_position / line_duration))

    def get_countdown_to_next_line(self) -> float:
        """
        Return time in seconds until next line
        Useful for showing countdown
        """
        if not self.song or not self.current_line:
            return 0.0

        # Find next line
        found_current = False
        for line in self.song.lines:
            if found_current:
                return self.song.get_beat_time(line.start) - self.current_time
            if line == self.current_line:
                found_current = True

        return 0.0
