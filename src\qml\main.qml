import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs
import UltraStarBasics 1.0

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "UltraStar Simple"

    property alias karaokeItem: karaokeDisplay

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10

        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true

            // Song list
            GroupBox {
                title: "Songs"
                Layout.preferredWidth: 300
                Layout.fillHeight: true

                ListView {
                    id: songListView
                    anchors.fill: parent
                    model: mainBackend.songList

                    delegate: ItemDelegate {
                        width: songListView.width
                        height: 40

                        Rectangle {
                            anchors.fill: parent
                            color: parent.hovered ? "#e0e0e0" : "transparent"
                            border.color: "#ccc"
                            border.width: 1

                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 10
                                anchors.verticalCenter: parent.verticalCenter
                                text: modelData.artist + " - " + modelData.title
                                elide: Text.ElideRight
                            }
                        }

                        onClicked: {
                            mainBackend.select_song(modelData.index)
                        }
                    }
                }
            }

            // Info and controls
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true

                // Song info
                GroupBox {
                    title: "Song Information"
                    Layout.fillWidth: true

                    RowLayout {
                        anchors.fill: parent

                        Text {
                            id: songInfoText
                            Layout.fillWidth: true
                            text: mainBackend.currentSongInfo
                            wrapMode: Text.WordWrap
                        }

                        Rectangle {
                            Layout.preferredWidth: 200
                            Layout.preferredHeight: 200
                            color: "#333"
                            border.color: "#666"
                            border.width: 1

                            Image {
                                anchors.fill: parent
                                anchors.margins: 2
                                source: mainBackend.currentCoverPath
                                fillMode: Image.PreserveAspectFit
                                visible: source != ""
                                
                                // Handle image loading errors silently
                                onStatusChanged: {
                                    if (status === Image.Error) {
                                        console.log("Cover image not found or could not be loaded")
                                    }
                                }
                            }

                            Text {
                                anchors.centerIn: parent
                                text: "No Cover"
                                color: "#999"
                                visible: !mainBackend.currentCoverPath || coverImage.status === Image.Error
                            }
                        }
                    }
                }

                // Playback controls
                GroupBox {
                    title: "Playback Controls"
                    Layout.fillWidth: true

                    ColumnLayout {
                        anchors.fill: parent

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "Play"
                                onClicked: mainBackend.play()
                            }

                            Button {
                                text: "Pause"
                                onClicked: mainBackend.pause()
                            }

                            Button {
                                text: "Stop"
                                onClicked: mainBackend.stop()
                            }

                            Button {
                                text: "Load Songs"
                                onClicked: mainBackend.load_songs_folder()
                            }
                        }

                        Slider {
                            id: progressSlider
                            Layout.fillWidth: true
                            from: 0
                            to: 1
                            value: mainBackend.duration > 0 ? mainBackend.position / mainBackend.duration : 0

                            onMoved: {
                                mainBackend.seek(value)
                            }
                        }

                        Text {
                            Layout.alignment: Qt.AlignHCenter
                            text: mainBackend.formatTime(mainBackend.position) + " / " + mainBackend.formatTime(mainBackend.duration)
                        }
                    }
                }

                // Karaoke options
                GroupBox {
                    title: "Karaoke Options"
                    Layout.fillWidth: true

                    ColumnLayout {
                        anchors.fill: parent

                        // Color controls
                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "Sung Color"
                                onClicked: sungColorDialog.open()
                            }

                            Button {
                                text: "Pending Color"
                                onClicked: pendingColorDialog.open()
                            }

                            Button {
                                text: "Ball Color"
                                onClicked: ballColorDialog.open()
                            }
                        }

                        // Effect selection
                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "Effect:"
                            }

                            ComboBox {
                                id: effectComboBox
                                Layout.fillWidth: true
                                model: ["Simple", "Zoom", "Particle", "Ball", "Shift", "Wave", "Pulse", "Typewriter", "Aegisub 3D"]
                                currentIndex: karaokeBackend ? karaokeBackend.currentEffect : 0

                                onCurrentIndexChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.setEffect(currentIndex)
                                    }
                                }
                            }
                        }

                        // Settings
                        GridLayout {
                            Layout.fillWidth: true
                            columns: 2

                            Text { text: "Transition Effect:" }
                            CheckBox {
                                checked: karaokeBackend ? karaokeBackend.transitionEffect : false
                                onCheckedChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.transitionEffect = checked
                                    }
                                }
                            }

                            Text { text: "Interpolation:" }
                            CheckBox {
                                checked: karaokeBackend ? karaokeBackend.interpolation : false
                                onCheckedChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.interpolation = checked
                                    }
                                }
                            }

                            Text { text: "Transition Speed:" }
                            Slider {
                                Layout.fillWidth: true
                                from: 1
                                to: 10
                                value: karaokeBackend ? karaokeBackend.transitionSpeed : 5
                                onValueChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.transitionSpeed = value
                                    }
                                }
                            }

                            Text { text: "Sync Offset (ms):" }
                            Slider {
                                Layout.fillWidth: true
                                from: -100
                                to: 100
                                value: karaokeBackend ? karaokeBackend.syncOffset * 1000 : 0
                                onValueChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.syncOffset = value / 1000
                                    }
                                }
                            }

                            Text { text: "Smoothing:" }
                            Slider {
                                Layout.fillWidth: true
                                from: 0
                                to: 1
                                value: karaokeBackend ? karaokeBackend.smoothingFactor : 0.5
                                onValueChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.smoothingFactor = value
                                    }
                                }
                            }

                            Text { text: "Anticipation (ms):" }
                            Slider {
                                Layout.fillWidth: true
                                from: 0
                                to: 50
                                value: karaokeBackend ? karaokeBackend.anticipationTime * 1000 : 0
                                onValueChanged: {
                                    if (karaokeBackend) {
                                        karaokeBackend.anticipationTime = value / 1000
                                    }
                                }
                            }
                        }

                        Button {
                            text: "Debug Info"
                            onClicked: debugDialog.open()
                        }
                    }
                }
            }
        }

        // Karaoke display
        KaraokeItem {
            id: karaokeDisplay
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            backend: karaokeBackend
        }
    }

    // Color dialogs
    ColorDialog {
        id: sungColorDialog
        title: "Select Sung Text Color"
        selectedColor: karaokeBackend ? karaokeBackend.sungColor : "blue"
        onAccepted: {
            if (karaokeBackend) {
                karaokeBackend.sungColor = selectedColor
            }
        }
    }

    ColorDialog {
        id: pendingColorDialog
        title: "Select Pending Text Color"
        selectedColor: karaokeBackend ? karaokeBackend.pendingColor : "black"
        onAccepted: {
            if (karaokeBackend) {
                karaokeBackend.pendingColor = selectedColor
            }
        }
    }

    ColorDialog {
        id: ballColorDialog
        title: "Select Ball Color"
        selectedColor: karaokeBackend ? karaokeBackend.ballColor : "red"
        onAccepted: {
            if (karaokeBackend) {
                karaokeBackend.ballColor = selectedColor
            }
        }
    }

    // Debug dialog
    Dialog {
        id: debugDialog
        title: "Debug Information"
        width: 500
        height: 400

        ScrollView {
            anchors.fill: parent
            TextArea {
                text: karaokeBackend ? karaokeBackend.getDebugInfo() : "No debug info available"
                readOnly: true
                wrapMode: TextArea.Wrap
            }
        }
    }
}
